# MCP User Guidelines - 基于您的配置

## 概述
本指南基于您当前的 MCP 配置文件，提供具体的使用方法和最佳实践。

## 您的 MCP 服务配置

### 1. Context7 (@upstash/context7-mcp)
**功能**: 获取最新的技术文档和库信息
**使用场景**:
- 查询 React、Vue、Next.js 等框架文档
- 获取 API 参考和代码示例
- 学习新技术栈的最佳实践

**使用示例**:
```
"获取 React Hooks 的最新文档"
"查找 Next.js 路由配置的示例"
"显示 TypeScript 接口定义的最佳实践"
```

### 2. Sequential Thinking
**功能**: 复杂问题的系统性分析和解决
**使用场景**:
- 分解复杂的开发任务
- 架构设计决策
- 问题诊断和解决方案制定

**使用示例**:
```
"分析实现用户认证系统的完整方案"
"设计微服务架构的步骤"
"诊断性能问题的系统方法"
```

### 3. MCP Feedback Enhanced (uvx)
**功能**: 增强的交互式反馈收集
**特点**: 
- 600秒超时设置
- 自动批准交互式反馈
- 支持图片和文本反馈

**使用场景**:
- 验证代码实现效果
- 收集用户界面反馈
- 测试功能是否正常工作

### 4. Playwright (@playwright/mcp)
**功能**: 浏览器自动化和测试
**使用场景**:
- E2E 测试自动化
- 网页截图和快照
- 表单填写和交互测试
- 性能测试

**使用示例**:
```
"为登录流程创建自动化测试"
"截取网页的特定区域"
"测试响应式设计在不同屏幕尺寸下的表现"
```

### 5. MCP Server Time (uvx)
**功能**: 时间和时区处理
**配置**: 本地时区设置为 Asia/Shanghai
**使用场景**:
- 获取当前时间
- 时区转换
- 定时任务规划

**使用示例**:
```
"获取北京时间"
"将美国东部时间转换为北京时间"
"计算两个时区的时间差"
```

### 6. Shrimp Task Manager
**功能**: 任务管理和项目规划
**配置**:
- 数据目录: `D:/workspace/Aother/mcp-shrimp-task-manager/data`
- 模板语言: 英文
- GUI 禁用

**使用场景**:
- 项目任务分解
- 进度跟踪
- 团队协作规划

**使用示例**:
```
"创建新项目的任务列表"
"更新任务状态"
"生成项目进度报告"
```

### 7. MCP DeepWiki
**功能**: 深度知识库和文档搜索
**使用场景**:
- 技术知识查询
- 概念解释
- 深度学习资料

**使用示例**:
```
"解释微服务架构的核心概念"
"查找机器学习算法的详细说明"
"获取软件设计模式的深度分析"
```

### 8. Brave Search
**功能**: 网络搜索
**配置**: 已设置 API 密钥
**使用场景**:
- 实时信息搜索
- 技术问题解决方案查找
- 市场调研

**使用示例**:
```
"搜索最新的 JavaScript 框架趋势"
"查找特定错误的解决方案"
"了解某个技术的社区讨论"
```

### 9. Fetch Server
**功能**: 网页内容获取和 Markdown 转换
**使用场景**:
- 获取网页内容
- 文档内容提取
- 内容格式转换

**使用示例**:
```
"获取某个技术博客的内容"
"提取 GitHub README 文件内容"
"转换网页内容为 Markdown 格式"
```

## 开发工作流程建议

### 1. 项目启动阶段
```
1. 使用 Sequential Thinking 分析项目需求
2. 使用 Shrimp Task Manager 创建任务计划
3. 使用 Context7 研究相关技术栈
4. 使用 DeepWiki 深入了解核心概念
```

### 2. 开发实施阶段
```
1. 使用 Context7 获取 API 文档和示例
2. 使用 Brave Search 查找解决方案
3. 使用 Fetch 获取参考资料
4. 使用 MCP Feedback Enhanced 验证实现
```

### 3. 测试验证阶段
```
1. 使用 Playwright 进行自动化测试
2. 使用 MCP Feedback Enhanced 收集反馈
3. 使用 Sequential Thinking 分析测试结果
4. 使用 Shrimp Task Manager 跟踪问题修复
```

## 服务组合使用策略

### 学习新技术
```
Context7 → DeepWiki → Sequential Thinking → MCP Feedback Enhanced
1. 获取官方文档
2. 深入理解概念
3. 制定学习计划
4. 实践验证
```

### 解决复杂问题
```
Sequential Thinking → Brave Search → Context7 → Playwright
1. 分析问题
2. 搜索解决方案
3. 查找具体实现
4. 自动化测试验证
```

### 项目管理
```
Shrimp Task Manager → Sequential Thinking → Time Server → MCP Feedback Enhanced
1. 创建任务计划
2. 分析执行步骤
3. 时间规划
4. 进度反馈
```

## 最佳实践

### 1. 环境配置
- 确保 uvx 和 npx 正常工作
- 验证 Brave API 密钥有效性
- 检查数据目录权限设置

### 2. 使用技巧
- **Context7**: 使用具体的库名和版本号查询
- **Sequential Thinking**: 提供充分的上下文信息
- **Playwright**: 先了解目标网站结构
- **Task Manager**: 定期更新任务状态

### 3. 故障排除
```bash
# 检查服务状态
npx @upstash/context7-mcp@latest --version
uvx mcp-feedback-enhanced@latest --version
npx @playwright/mcp@latest --version

# 验证环境变量
echo $BRAVE_API_KEY

# 检查数据目录
ls -la "D:/workspace/Aother/mcp-shrimp-task-manager/data"
```

## 安全注意事项

### 1. API 密钥管理
- Brave API 密钥已在配置中，注意保护
- 不要在公共仓库中提交包含密钥的配置

### 2. 数据隐私
- Task Manager 数据存储在本地
- 注意敏感信息的处理

### 3. 网络安全
- Fetch 和 Brave Search 会访问外部网络
- 注意验证获取内容的可信度

## 性能优化

### 1. 超时设置
- MCP Feedback Enhanced 设置了 600 秒超时
- 根据需要调整其他服务的超时时间

### 2. 缓存策略
- Context7 文档会有缓存
- 定期清理不必要的数据

### 3. 并发使用
- 可以同时使用多个服务
- 注意 API 调用频率限制

## 常见使用场景

### 1. 全栈开发项目
```
1. 项目规划 (Shrimp Task Manager)
2. 技术选型 (Context7 + DeepWiki)
3. 开发实施 (Context7 + Brave Search)
4. 测试验证 (Playwright + MCP Feedback Enhanced)
5. 部署上线 (Sequential Thinking + Time Server)
```

### 2. 学习新框架
```
1. 概念理解 (DeepWiki)
2. 官方文档 (Context7)
3. 实践项目 (Sequential Thinking)
4. 问题解决 (Brave Search + Fetch)
5. 进度跟踪 (Shrimp Task Manager)
```

### 3. 问题调试
```
1. 问题分析 (Sequential Thinking)
2. 解决方案搜索 (Brave Search)
3. 文档查询 (Context7)
4. 测试验证 (Playwright)
5. 结果确认 (MCP Feedback Enhanced)
```

## 更新和维护

### 1. 定期更新
```bash
# 更新到最新版本
npx @upstash/context7-mcp@latest
uvx mcp-feedback-enhanced@latest
npx @playwright/mcp@latest
```

### 2. 配置备份
- 定期备份 mcp-config.json
- 备份 Task Manager 数据目录

### 3. 监控使用情况
- 关注 API 调用次数
- 监控服务响应时间
- 定期清理日志文件

---

*本指南基于您当前的 MCP 配置文件编写，建议根据实际使用情况进行调整和优化。*
